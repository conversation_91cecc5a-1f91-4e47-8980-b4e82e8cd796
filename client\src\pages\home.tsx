import { useEffect, useRef } from "react";
import AnimatedLetters from "@/components/AnimatedLetters";
import ParticleBackground from "@/components/ParticleBackground";
import RotatingTitle from "@/components/RotatingTitle";
import PortfolioSection from "@/components/PortfolioSection";
import UFOAnimation from "@/components/UFOAnimation";

declare global {
  interface Window {
    gsap: any;
  }
}

export default function Home() {
  const subtitleRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  const navDotsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== "undefined" && window.gsap) {
      const timeline = window.gsap.timeline();
      
      // Animate subtitle after letters complete their initial animation
      timeline.to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out"
      }, 2.5);
      
      // Animate navigation dots
      timeline.to(navDotsRef.current, {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: "power2.out"
      }, 3);
      
      // Animate scroll indicator
      timeline.to(scrollIndicatorRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }, 3.5);
    }
  }, []);

  const handleNavDotClick = (index: number) => {
    if (typeof window !== "undefined" && window.gsap) {
      const colors = ['#FF3366', '#4A90E2', '#F5F5F5'];
      const letters = document.querySelectorAll('.letter');
      
      letters.forEach((letter, letterIndex) => {
        window.gsap.to(letter, {
          color: colors[index],
          scale: 1.1,
          duration: 0.3,
          delay: letterIndex * 0.05,
          yoyo: true,
          repeat: 1,
          ease: "power2.inOut"
        });
      });
    }
  };

  return (
    <>
      <div className="min-h-screen bg-[#1A1A1A] font-montserrat overflow-hidden relative">
        {/* Background with gradient */}
        <div className="fixed inset-0 bg-gradient-radial" />
        
        {/* Particle Background */}
        <ParticleBackground />
        
        {/* UFO Animation */}
        <UFOAnimation />
        
        {/* Main Content */}
        <main className="relative z-10 min-h-screen flex items-center justify-center">
          <div className="text-center px-4">
            {/* Main Typography */}
            <div className="mb-8">
              <AnimatedLetters />
            </div>
            
            {/* Subtitle */}
            <div 
              ref={subtitleRef}
              className="opacity-0 transform translate-y-4"
            >
              <RotatingTitle />
              
              {/* Navigation Links */}
              <div className="flex justify-center space-x-8 text-sm">
                <a 
                  href="#portfolio" 
                  className="text-[#F5F5F5] hover:text-[#FF3366] transition-colors duration-300 tracking-wider uppercase"
                >
                  Portfolio
                </a>
                <a 
                  href="#about" 
                  className="text-[#F5F5F5] hover:text-[#4A90E2] transition-colors duration-300 tracking-wider uppercase"
                >
                  About
                </a>
                <a 
                  href="#contact" 
                  className="text-[#F5F5F5] hover:text-[#FF3366] transition-colors duration-300 tracking-wider uppercase"
                >
                  Contact
                </a>
              </div>
            </div>
          </div>
        </main>
        
        {/* Navigation Dots */}
        <div 
          ref={navDotsRef}
          className="fixed right-8 top-1/2 transform -translate-y-1/2 space-y-4 opacity-0 translate-x-4"
        >
          <div 
            className="w-2 h-2 bg-[#FF3366] rounded-full cursor-pointer hover:scale-150 transition-transform duration-300"
            onClick={() => handleNavDotClick(0)}
          />
          <div 
            className="w-2 h-2 bg-[#4A90E2] rounded-full cursor-pointer hover:scale-150 transition-transform duration-300"
            onClick={() => handleNavDotClick(1)}
          />
          <div 
            className="w-2 h-2 bg-[#F5F5F5] rounded-full cursor-pointer hover:scale-150 transition-transform duration-300"
            onClick={() => handleNavDotClick(2)}
          />
        </div>
        
        {/* Scroll Indicator */}
        <div 
          ref={scrollIndicatorRef}
          className="fixed bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 translate-y-4"
        >
          <div className="flex flex-col items-center space-y-2">
            <div className="w-px h-16 bg-gradient-to-b from-transparent to-[#FF3366]" />
            <p className="text-xs font-light tracking-widest text-gray-500 uppercase transform rotate-90 origin-center">
              Explore
            </p>
          </div>
        </div>
      </div>
      
      {/* Portfolio Section */}
      <PortfolioSection />
    </>
  );
}
