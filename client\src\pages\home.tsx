import { useEffect, useRef, useCallback } from "react";
import { useGS<PERSON> } from '@/hooks/useGSAP';
import { useError<PERSON>and<PERSON> } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  ANIMATION_DURATIONS,
  ANIMATION_DELAYS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  NAVIGATION_ITEMS,
  Z_INDEX
} from '@/constants';
import AnimatedLetters from "@/components/AnimatedLetters";
import ParticleBackground from "@/components/ParticleBackground";
import RotatingTitle from "@/components/RotatingTitle";
import PortfolioSection from "@/components/PortfolioSection";
import UFOAnimation from "@/components/UFOAnimation";

export default function Home() {
  const subtitleRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  const navDotsRef = useRef<HTMLDivElement>(null);
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const initializePageAnimations = useCallback(() => {
    try {
      if (!isAvailable) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Set final states immediately for reduced motion
        if (subtitleRef.current) {
          animate(subtitleRef.current, { opacity: 1, y: 0, duration: 0 });
        }
        if (navDotsRef.current) {
          animate(navDotsRef.current, { opacity: 1, x: 0, duration: 0 });
        }
        if (scrollIndicatorRef.current) {
          animate(scrollIndicatorRef.current, { opacity: 1, y: 0, duration: 0 });
        }
        return;
      }

      const timeline = gsap?.timeline();
      if (!timeline) return;

      // Animate subtitle after letters complete their initial animation
      timeline.to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 2.5);

      // Animate navigation dots
      timeline.to(navDotsRef.current, {
        opacity: 1,
        x: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 3);

      // Animate scroll indicator
      timeline.to(scrollIndicatorRef.current, {
        opacity: 1,
        y: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 3.5);

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, animate, handleError]);

  useEffect(() => {
    if (isAvailable) {
      initializePageAnimations();
    }
  }, [isAvailable, initializePageAnimations]);

  const handleNavDotClick = useCallback((index: number) => {
    try {
      if (!isAvailable) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      const colors = [COLOR_VARIANTS.PINK, COLOR_VARIANTS.BLUE, COLOR_VARIANTS.WHITE];
      const letters = document.querySelectorAll('.letter');

      letters.forEach((letter, letterIndex) => {
        animate(letter, {
          color: colors[index],
          scale: 1.1,
          duration: ANIMATION_DURATIONS.NORMAL,
          delay: letterIndex * 0.05,
          yoyo: true,
          repeat: 1,
          ease: ANIMATION_EASINGS.POWER2_IN_OUT
        });
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, animate, handleError]);

  const handleNavigationClick = useCallback((href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }, []);

  return (
    <>
      <div className="min-h-screen bg-background font-montserrat overflow-hidden relative">
        {/* Background with gradient */}
        <div
          className="fixed inset-0 bg-gradient-radial"
          style={{ zIndex: Z_INDEX.BACKGROUND }}
          role="presentation"
          aria-hidden="true"
        />

        {/* Particle Background */}
        <ParticleBackground />

        {/* UFO Animation */}
        <UFOAnimation />

        {/* Main Content */}
        <main
          className="relative min-h-screen flex items-center justify-center"
          style={{ zIndex: Z_INDEX.CONTENT }}
          role="main"
        >
          <div className="text-center px-4 max-w-4xl mx-auto">
            {/* Main Typography */}
            <header className="mb-8">
              <AnimatedLetters />
            </header>

            {/* Subtitle */}
            <div
              ref={subtitleRef}
              className="opacity-0 transform translate-y-4"
            >
              <RotatingTitle />

              {/* Navigation Links */}
              <nav
                className="flex justify-center space-x-8 text-sm"
                role="navigation"
                aria-label="Main navigation"
              >
                {NAVIGATION_ITEMS.map((item, index) => (
                  <button
                    key={item.href}
                    onClick={() => handleNavigationClick(item.href)}
                    className="text-foreground hover:text-primary transition-colors duration-300 tracking-wider uppercase focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background"
                    style={{
                      '--hover-color': item.color
                    } as React.CSSProperties}
                    aria-label={`Navigate to ${item.label} section`}
                  >
                    {item.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </main>

        {/* Navigation Dots */}
        <aside
          ref={navDotsRef}
          className="fixed right-8 top-1/2 transform -translate-y-1/2 space-y-4 opacity-0 translate-x-4"
          style={{ zIndex: Z_INDEX.NAVIGATION }}
          role="complementary"
          aria-label="Color theme selector"
        >
          {[
            { color: COLOR_VARIANTS.PINK, label: 'Pink theme' },
            { color: COLOR_VARIANTS.BLUE, label: 'Blue theme' },
            { color: COLOR_VARIANTS.WHITE, label: 'White theme' }
          ].map((theme, index) => (
            <button
              key={index}
              className="w-3 h-3 rounded-full cursor-pointer hover:scale-150 transition-transform duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background"
              style={{ backgroundColor: theme.color }}
              onClick={() => handleNavDotClick(index)}
              aria-label={theme.label}
              title={theme.label}
            />
          ))}
        </aside>

        {/* Scroll Indicator */}
        <div
          ref={scrollIndicatorRef}
          className="fixed bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 translate-y-4"
          style={{ zIndex: Z_INDEX.CONTENT }}
          role="presentation"
          aria-hidden="true"
        >
          <div className="flex flex-col items-center space-y-2">
            <div
              className="w-px h-16 bg-gradient-to-b from-transparent to-primary"
              role="presentation"
            />
            <p className="text-xs font-light tracking-widest text-muted-foreground uppercase transform rotate-90 origin-center">
              Explore
            </p>
          </div>
        </div>
      </div>

      {/* Portfolio Section */}
      <PortfolioSection />
    </>
  );
}
