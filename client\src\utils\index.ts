/**
 * Utility functions for the portfolio website
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { AccessibilityConfig, PerformanceMetrics } from '@/types';
import { ACCESSIBILITY_SETTINGS, PERFORMANCE_THRESHOLDS } from '@/constants';

/**
 * Combines class names using clsx and tailwind-merge
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Checks if the user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia(ACCESSIBILITY_SETTINGS.REDUCED_MOTION_QUERY).matches;
}

/**
 * Checks if the user prefers high contrast
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia(ACCESSIBILITY_SETTINGS.HIGH_CONTRAST_QUERY).matches;
}

/**
 * Gets accessibility configuration based on user preferences
 */
export function getAccessibilityConfig(): AccessibilityConfig {
  return {
    reduceMotion: prefersReducedMotion(),
    highContrast: prefersHighContrast(),
    screenReader: isScreenReaderActive(),
  };
}

/**
 * Detects if a screen reader is likely active
 */
export function isScreenReaderActive(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check for common screen reader indicators
  const hasAriaLive = document.querySelector('[aria-live]') !== null;
  const hasScreenReaderClass = document.body.classList.contains('screen-reader');
  const hasReducedMotion = prefersReducedMotion();
  
  return hasAriaLive || hasScreenReaderClass || hasReducedMotion;
}

/**
 * Safely gets element bounds with fallback
 */
export function getElementBounds(element: Element | null): DOMRect {
  if (!element) {
    return new DOMRect(0, 0, 0, 0);
  }
  return element.getBoundingClientRect();
}

/**
 * Calculates the center point of an element
 */
export function getElementCenter(element: Element | null): { x: number; y: number } {
  const bounds = getElementBounds(element);
  return {
    x: bounds.left + bounds.width / 2,
    y: bounds.top + bounds.height / 2,
  };
}

/**
 * Generates a random number between min and max
 */
export function randomBetween(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

/**
 * Generates a random integer between min and max (inclusive)
 */
export function randomIntBetween(min: number, max: number): number {
  return Math.floor(randomBetween(min, max + 1));
}

/**
 * Picks a random item from an array
 */
export function randomChoice<T>(array: readonly T[]): T {
  return array[randomIntBetween(0, array.length - 1)];
}

/**
 * Debounces a function call
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttles a function call
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Checks if GSAP is available
 */
export function isGSAPAvailable(): boolean {
  return typeof window !== 'undefined' && typeof window.gsap !== 'undefined';
}

/**
 * Safely executes GSAP animations with fallback
 */
export function safeGSAPCall(callback: () => void, fallback?: () => void): void {
  if (isGSAPAvailable()) {
    try {
      callback();
    } catch (error) {
      console.warn('GSAP animation failed:', error);
      fallback?.();
    }
  } else {
    fallback?.();
  }
}

/**
 * Creates a performance monitor
 */
export function createPerformanceMonitor(): {
  start: () => void;
  end: () => PerformanceMetrics;
} {
  let startTime: number;
  let startMemory: number;

  return {
    start: () => {
      startTime = performance.now();
      startMemory = (performance as any).memory?.usedJSHeapSize || 0;
    },
    end: () => {
      const endTime = performance.now();
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      return {
        renderTime: endTime - startTime,
        memoryUsage: (endMemory - startMemory) / 1024 / 1024, // Convert to MB
        animationFrameRate: 1000 / (endTime - startTime),
      };
    },
  };
}

/**
 * Checks if performance is within acceptable thresholds
 */
export function isPerformanceAcceptable(metrics: PerformanceMetrics): boolean {
  return (
    metrics.animationFrameRate >= PERFORMANCE_THRESHOLDS.MIN_FRAME_RATE &&
    metrics.memoryUsage <= PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE
  );
}

/**
 * Formats a number with appropriate units
 */
export function formatNumber(num: number, decimals: number = 1): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(decimals) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(decimals) + 'K';
  }
  return num.toFixed(decimals);
}

/**
 * Creates a cleanup function for event listeners
 */
export function createEventCleanup(): {
  add: (element: Element, event: string, handler: EventListener) => void;
  cleanup: () => void;
} {
  const listeners: Array<{
    element: Element;
    event: string;
    handler: EventListener;
  }> = [];

  return {
    add: (element: Element, event: string, handler: EventListener) => {
      element.addEventListener(event, handler);
      listeners.push({ element, event, handler });
    },
    cleanup: () => {
      listeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      listeners.length = 0;
    },
  };
}

/**
 * Validates if an element is in the viewport
 */
export function isInViewport(element: Element, threshold: number = 0): boolean {
  const bounds = getElementBounds(element);
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;

  return (
    bounds.top < windowHeight - threshold &&
    bounds.bottom > threshold &&
    bounds.left < windowWidth - threshold &&
    bounds.right > threshold
  );
}
