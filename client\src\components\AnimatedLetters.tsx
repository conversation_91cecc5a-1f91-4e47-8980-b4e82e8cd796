import { useEffect, useRef } from "react";

declare global {
  interface Window {
    gsap: any;
  }
}

export default function AnimatedLetters() {
  const containerRef = useRef<HTMLDivElement>(null);
  const lettersInitialized = useRef(false);

  useEffect(() => {
    if (typeof window !== "undefined" && window.gsap && !lettersInitialized.current) {
      lettersInitialized.current = true;
      initializeAnimations();
    }
  }, []);

  const initializeAnimations = () => {
    const letters = document.querySelectorAll('.letter');
    
    // Clear any existing animations first
    window.gsap.killTweensOf(letters);
    
    // Reset all letters to initial state
    window.gsap.set(letters, {
      opacity: 0,
      y: 100,
      x: 0,
      rotation: 0,
      rotationX: 90,
      rotationY: 0,
      rotationZ: 0,
      scale: 1,
      transformOrigin: "center bottom"
    });

    const timeline = window.gsap.timeline();

    // Simple, consistent reveal animation for all letters
    letters.forEach((letter: any, index: number) => {
      const delay = index * 0.1;
      
      timeline.to(letter, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 0.8,
        ease: "power2.out",
        delay: delay
      }, 0);
    });

    // Setup hover effects after initial animation
    setTimeout(() => {
      setupHoverEffects();
      startContinuousLoop();
    }, 2000);
  };

  const setupHoverEffects = () => {
    const letters = document.querySelectorAll('.letter');
    
    letters.forEach((letter: any, index: number) => {
      const hoverEffectType = index % 5;
      
      letter.addEventListener('mouseenter', () => {
        switch (hoverEffectType) {
          case 0: // Glow and scale with motion blur (pink)
            window.gsap.timeline()
              .to(letter, {
                filter: "blur(3px) brightness(1.5)",
                scale: 1.2,
                color: "#FF3366",
                textShadow: "0 0 20px rgba(255, 51, 102, 0.8), 0 0 40px rgba(255, 51, 102, 0.5)",
                rotation: Math.random() * 20 - 10,
                duration: 0.15,
                ease: "power2.out"
              })
              .to(letter, {
                filter: "blur(0px) brightness(1)",
                duration: 0.25,
                ease: "power2.out"
              });
            break;
          case 1: // Cinematic rotation with depth blur (blue)
            window.gsap.timeline()
              .to(letter, {
                filter: "blur(4px) saturate(1.5)",
                rotation: 180,
                scale: 1.1,
                color: "#4A90E2",
                textShadow: "0 0 20px rgba(74, 144, 226, 0.8)",
                duration: 0.25,
                ease: "power2.inOut"
              })
              .to(letter, {
                rotation: 360,
                filter: "blur(0px) saturate(1)",
                duration: 0.25,
                ease: "power2.out"
              });
            break;
          case 2: // Bounce with depth of field (blue)
            window.gsap.timeline()
              .to(letter, {
                y: -10,
                filter: "blur(2px) brightness(1.3)",
                textShadow: "0 0 30px rgba(74, 144, 226, 0.8)",
                color: "#4A90E2",
                scale: 1.15,
                duration: 0.2,
                ease: "power2.out"
              })
              .to(letter, {
                filter: "blur(0px) brightness(1)",
                duration: 0.2,
                ease: "bounce.out"
              });
            break;
          case 3: // Cinematic flip with motion blur (pink)
            window.gsap.timeline()
              .to(letter, {
                rotationY: 90,
                filter: "blur(5px) brightness(1.4)",
                scale: 1.3,
                z: 50,
                duration: 0.2,
                ease: "power2.in"
              })
              .to(letter, {
                rotationY: 180,
                color: "#FF3366",
                textShadow: "0 0 25px rgba(255, 51, 102, 0.8)",
                filter: "blur(0px) brightness(1)",
                z: 0,
                duration: 0.2,
                ease: "power2.out"
              });
            break;
          case 4: // Pulse with cinematic blur
            window.gsap.timeline()
              .to(letter, {
                scale: 1.15,
                filter: "blur(3px) saturate(1.4)",
                textShadow: "0 0 25px currentColor",
                duration: 0.15,
                ease: "power2.inOut"
              })
              .to(letter, {
                scale: 1,
                filter: "blur(0px) saturate(1)",
                duration: 0.15,
                ease: "power2.inOut"
              })
              .to(letter, {
                scale: 1.15,
                filter: "blur(1px) brightness(1.2)",
                duration: 0.15,
                ease: "power2.inOut"
              })
              .to(letter, {
                scale: 1,
                filter: "blur(0px) brightness(1)",
                duration: 0.15,
                ease: "power2.inOut"
              });
            break;
        }
      });
      
      letter.addEventListener('mouseleave', () => {
        window.gsap.to(letter, {
          scale: 1,
          rotation: 0,
          rotationY: 0,
          y: 0,
          z: 0,
          color: "#F5F5F5",
          textShadow: "none",
          filter: "blur(0px) brightness(1) saturate(1)",
          duration: 0.3,
          ease: "power2.out"
        });
      });
      
      // Cinematic click effect with motion blur
      letter.addEventListener('click', () => {
        window.gsap.timeline()
          .to(letter, {
            scale: 0.8,
            rotation: 180,
            filter: "blur(4px) brightness(1.6)",
            duration: 0.1,
            ease: "power2.in"
          })
          .to(letter, {
            scale: 1.2,
            rotation: 360,
            filter: "blur(1px) saturate(1.5)",
            duration: 0.2,
            ease: "power2.out"
          })
          .to(letter, {
            scale: 1,
            rotation: 0,
            filter: "blur(0px) brightness(1) saturate(1)",
            duration: 0.3,
            ease: "back.out(1.7)"
          });
      });
    });
  };

  const startContinuousLoop = () => {
    const letters = document.querySelectorAll('.letter');
    const loopTimeline = window.gsap.timeline({ repeat: -1, repeatDelay: 4 });
    
    letters.forEach((letter: any, index: number) => {
      const delay = index * 0.05;
      
      // Gentle floating animation that doesn't displace letters
      loopTimeline.to(letter, {
        y: -10,
        rotationY: 180,
        color: index % 2 === 0 ? '#FF3366' : '#4A90E2',
        duration: 0.8,
        ease: "power2.inOut",
        delay: delay
      }, 0);
      
      loopTimeline.to(letter, {
        y: 0,
        rotationY: 360,
        color: '#F5F5F5',
        duration: 0.8,
        ease: "power2.inOut"
      }, 1.2 + delay);
    });
  };

  return (
    <div ref={containerRef} className="letter-container" style={{ perspective: '1000px' }}>
      <h1 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-black tracking-wider leading-none text-[#F5F5F5]">
        <div className="mb-4 flex justify-center">
          <span className="letter inline-block" data-letter="J">J</span>
          <span className="letter inline-block" data-letter="A">A</span>
          <span className="letter inline-block" data-letter="I">I</span>
          <span className="letter inline-block" data-letter="M">M</span>
          <span className="letter inline-block" data-letter="E">E</span>
        </div>
        <div className="flex justify-center">
          <span className="letter inline-block" data-letter="R">R</span>
          <span className="letter inline-block" data-letter="Y">Y</span>
          <span className="letter inline-block" data-letter="A">A</span>
          <span className="letter inline-block" data-letter="N">N</span>
        </div>
      </h1>
    </div>
  );
}
