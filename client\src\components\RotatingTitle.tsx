import { useEffect, useRef, useState } from "react";

declare global {
  interface Window {
    gsap: any;
  }
}

const titles = [
  "Creative Developer & Designer",
  "AI-First App Builder", 
  "Full-Stack Developer",
  "Web Innovator & Experience Crafter"
];

export default function RotatingTitle() {
  const titleRef = useRef<HTMLParagraphElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const animationInitialized = useRef(false);

  useEffect(() => {
    if (typeof window !== "undefined" && window.gsap && !animationInitialized.current) {
      animationInitialized.current = true;
      startTitleRotation();
    }
  }, []);

  const startTitleRotation = () => {
    if (!titleRef.current) return;

    // Set initial state with proper filters and scale
    window.gsap.set(titleRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      textShadow: "none"
    });

    // Start the rotation cycle after initial page load
    setTimeout(() => {
      cycleTitles();
    }, 4000); // Wait 4 seconds before starting rotation
  };

  const cycleTitles = () => {
    if (!titleRef.current) return;

    const timeline = window.gsap.timeline({
      onComplete: () => {
        // Continue cycling
        setTimeout(cycleTitles, 3000); // 3 second pause between cycles
      }
    });

    // Exit animation for current title
    timeline.to(titleRef.current, {
      opacity: 0,
      y: -10,
      filter: "blur(4px)",
      duration: 0.5,
      ease: "power2.in"
    });

    // Update to next title
    timeline.call(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % titles.length);
    });

    // Entrance animation for new title with cinematic effects
    timeline.fromTo(titleRef.current, 
      { 
        opacity: 0, 
        y: 10, 
        scale: 0.95,
        filter: "blur(4px)"
      },
      { 
        opacity: 1, 
        y: 0,
        scale: 1,
        filter: "blur(0px)",
        textShadow: "0 0 20px rgba(255, 255, 255, 0.3), 0 0 40px rgba(74, 144, 226, 0.2)",
        duration: 0.5,
        ease: "power2.out"
      }
    );

    // Remove glow after entrance
    timeline.to(titleRef.current, {
      textShadow: "none",
      duration: 0.3,
      ease: "power2.out"
    }, "+=0.5");
  };

  return (
    <p 
      ref={titleRef}
      className="text-lg md:text-xl font-light tracking-widest text-gray-400 uppercase mb-8"
      style={{
        willChange: 'transform, opacity, filter',
        transformStyle: 'preserve-3d'
      }}
    >
      {titles[currentIndex]}
    </p>
  );
}