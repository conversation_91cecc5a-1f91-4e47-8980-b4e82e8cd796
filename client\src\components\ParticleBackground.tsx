import { useEffect, useRef } from "react";

declare global {
  interface Window {
    gsap: any;
  }
}

export default function ParticleBackground() {
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesInitialized = useRef(false);

  useEffect(() => {
    if (typeof window !== "undefined" && window.gsap && !particlesInitialized.current && containerRef.current) {
      particlesInitialized.current = true;
      createParticles();
    }
  }, []);

  const createParticles = () => {
    if (!containerRef.current) return;
    
    const container = containerRef.current;
    const particleCount = 50;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      container.appendChild(particle);
      
      // Floating animation
      window.gsap.to(particle, {
        y: -window.innerHeight - 100,
        duration: Math.random() * 20 + 10,
        repeat: -1,
        ease: "none",
        delay: Math.random() * 20
      });
      
      // Opacity animation
      window.gsap.to(particle, {
        opacity: Math.random() * 0.5 + 0.3,
        duration: Math.random() * 3 + 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });
      
      // Slight horizontal movement
      window.gsap.to(particle, {
        x: Math.random() * 40 - 20,
        duration: Math.random() * 5 + 3,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut"
      });
    }
  };

  return (
    <div 
      ref={containerRef}
      id="particles-container" 
      className="fixed inset-0 pointer-events-none z-0"
    />
  );
}
